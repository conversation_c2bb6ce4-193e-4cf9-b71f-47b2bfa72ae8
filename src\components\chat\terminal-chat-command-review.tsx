/**
 * Terminal Chat Command Review Component
 * 
 * Provides command approval interface with security assessment,
 * risk analysis, and user decision handling.
 */

import blessed from 'blessed';
import { formatCommandForDisplay, getCommandCategory } from '../../format-command.js';
import { 
  getSecurityAssessment, 
  formatApprovalRequest, 
  getApprovalPrompt,
  parseApprovalInput,
  ReviewDecision,
  type ApprovalRequest, 
} from '../../approvals.js';
import { useConfirmation } from '../../hooks/use-confirmation.js';

export interface CommandReviewOptions {
  parent: blessed.Widgets.Node;
  request: ApprovalRequest;
  onDecision?: (decision: ReviewDecision, alwaysApprove?: boolean) => void;
  onExplain?: () => void;
}

export class TerminalChatCommandReview {
  private container: blessed.Widgets.BoxElement;
  private headerBox: blessed.Widgets.BoxElement;
  private commandBox: blessed.Widgets.BoxElement;
  private securityBox: blessed.Widgets.BoxElement;
  private inputBox: blessed.Widgets.TextboxElement;
  private statusBox: blessed.Widgets.BoxElement;
  private options: CommandReviewOptions;
  private request: ApprovalRequest;

  constructor(options: CommandReviewOptions) {
    this.options = options;
    this.request = options.request;

    // Create main container
    this.container = blessed.box({
      parent: options.parent,
      top: 'center',
      left: 'center',
      width: '80%',
      height: '70%',
      border: { type: 'line' },
      style: {
        border: { fg: 'yellow' },
        bg: 'black',
      },
      label: ' Command Approval Required ',
      tags: true,
      mouse: true,
      keys: true,
    });

    this.createComponents();
    this.setupEventHandlers();
    this.render();
  }

  /**
   * Create UI components
   */
  private createComponents(): void {
    // Header with command info
    this.headerBox = blessed.box({
      parent: this.container,
      top: 0,
      left: 0,
      width: '100%',
      height: 4,
      border: { type: 'line' },
      style: {
        border: { fg: 'cyan' },
      },
      label: ' Command Details ',
      padding: { left: 1, right: 1 },
      tags: true,
    });

    // Command display
    this.commandBox = blessed.box({
      parent: this.container,
      top: 4,
      left: 0,
      width: '100%',
      height: 6,
      border: { type: 'line' },
      style: {
        border: { fg: 'white' },
      },
      label: ' Command ',
      padding: { left: 1, right: 1 },
      tags: true,
      scrollable: true,
    });

    // Security assessment
    this.securityBox = blessed.box({
      parent: this.container,
      top: 10,
      left: 0,
      width: '100%',
      height: '100%-14',
      border: { type: 'line' },
      style: {
        border: { fg: 'red' },
      },
      label: ' Security Assessment ',
      padding: { left: 1, right: 1 },
      tags: true,
      scrollable: true,
    });

    // Input box for user decision
    this.inputBox = blessed.textbox({
      parent: this.container,
      bottom: 3,
      left: 0,
      width: '100%',
      height: 3,
      border: { type: 'line' },
      style: {
        border: { fg: 'green' },
        focus: { border: { fg: 'yellow' } },
      },
      label: ' Your Decision ',
      inputOnFocus: true,
      keys: true,
      mouse: true,
    });

    // Status/help box
    this.statusBox = blessed.box({
      parent: this.container,
      bottom: 0,
      left: 0,
      width: '100%',
      height: 3,
      style: {
        fg: 'gray',
      },
      content: 'Options: (y)es, (n)o, (a)lways, (e)xplain, (q)uit | Press Enter to confirm',
      tags: true,
    });
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    // Handle input submission
    this.inputBox.on('submit', (value: string) => {
      this.handleDecision(value);
    });

    // Handle key shortcuts
    this.container.key(['y'], () => {
      this.handleDecision('y');
    });

    this.container.key(['n'], () => {
      this.handleDecision('n');
    });

    this.container.key(['a'], () => {
      this.handleDecision('a');
    });

    this.container.key(['e'], () => {
      this.handleDecision('e');
    });

    this.container.key(['q'], () => {
      this.handleDecision('q');
    });

    this.container.key(['escape'], () => {
      this.handleDecision('q');
    });

    // Focus input by default
    this.inputBox.focus();
  }

  /**
   * Render all components
   */
  private render(): void {
    this.renderHeader();
    this.renderCommand();
    this.renderSecurityAssessment();
  }

  /**
   * Render header information
   */
  private renderHeader(): void {
    const lines: string[] = [];
    
    lines.push(`{bold}Category:{/bold} ${this.request.category}`);
    lines.push(`{bold}Working Directory:{/bold} ${this.request.workdir}`);
    
    if (this.request.explanation) {
      lines.push(`{bold}Explanation:{/bold} ${this.request.explanation}`);
    }
    
    if (this.request.isDangerous) {
      lines.push('{red-fg}{bold}⚠️  WARNING: This command is potentially dangerous!{/bold}{/red-fg}');
    }

    this.headerBox.setContent(lines.join('\n'));
  }

  /**
   * Render command details
   */
  private renderCommand(): void {
    const commandStr = formatCommandForDisplay(this.request.command);
    const lines: string[] = [];
    
    lines.push(`{cyan-fg}${commandStr}{/cyan-fg}`);
    lines.push('');
    
    // Show individual arguments
    if (this.request.command.length > 1) {
      lines.push('{bold}Arguments:{/bold}');
      for (let i = 1; i < this.request.command.length; i++) {
        lines.push(`  ${i}: ${this.request.command[i]}`);
      }
    }

    this.commandBox.setContent(lines.join('\n'));
  }

  /**
   * Render security assessment
   */
  private renderSecurityAssessment(): void {
    const assessment = getSecurityAssessment(this.request.command);
    const lines: string[] = [];
    
    // Risk level
    const riskColors = {
      low: 'green-fg',
      medium: 'yellow-fg',
      high: 'red-fg',
      critical: 'red-fg',
    };
    
    const riskColor = riskColors[assessment.riskLevel];
    lines.push(`{bold}Risk Level:{/bold} {${riskColor}}${assessment.riskLevel.toUpperCase()}{/${riskColor}}`);
    lines.push('');
    
    // Risk reasons
    if (assessment.reasons.length > 0) {
      lines.push('{bold}Risk Factors:{/bold}');
      for (const reason of assessment.reasons) {
        lines.push(`  • ${reason}`);
      }
      lines.push('');
    }
    
    // Recommendations
    if (assessment.recommendations.length > 0) {
      lines.push('{bold}Recommendations:{/bold}');
      for (const recommendation of assessment.recommendations) {
        lines.push(`  • ${recommendation}`);
      }
      lines.push('');
    }
    
    // Additional context
    lines.push('{bold}Command Analysis:{/bold}');
    lines.push(`  • Command: ${this.request.command[0]}`);
    lines.push(`  • Category: ${this.request.category}`);
    lines.push(`  • Arguments: ${this.request.command.length - 1}`);
    
    // File system impact
    const fsCommands = ['rm', 'mv', 'cp', 'mkdir', 'rmdir', 'chmod', 'chown'];
    const baseCommand = this.request.command[0].split('/').pop()?.toLowerCase();
    if (baseCommand && fsCommands.includes(baseCommand)) {
      lines.push('  • {yellow-fg}Modifies file system{/yellow-fg}');
    }
    
    // Network impact
    const networkCommands = ['curl', 'wget', 'ssh', 'scp', 'rsync'];
    if (baseCommand && networkCommands.includes(baseCommand)) {
      lines.push('  • {yellow-fg}Network operation{/yellow-fg}');
    }

    this.securityBox.setContent(lines.join('\n'));
  }

  /**
   * Handle user decision
   */
  private handleDecision(input: string): void {
    const decision = parseApprovalInput(input);
    
    switch (decision) {
    case ReviewDecision.YES:
      this.submitDecision(decision, false);
      break;
    case ReviewDecision.ALWAYS:
      this.submitDecision(ReviewDecision.YES, true);
      break;
    case ReviewDecision.NO_CONTINUE:
    case ReviewDecision.NO_EXIT:
      this.submitDecision(decision, false);
      break;
    case ReviewDecision.EXPLAIN:
      if (this.options.onExplain) {
        this.options.onExplain();
      }
      break;
    default:
      this.updateStatus('Invalid option. Please choose: y/n/a/e/q');
      return;
    }
  }

  /**
   * Submit decision to parent
   */
  private submitDecision(decision: ReviewDecision, alwaysApprove: boolean): void {
    if (this.options.onDecision) {
      this.options.onDecision(decision, alwaysApprove);
    }
    this.destroy();
  }

  /**
   * Update status message
   */
  private updateStatus(message: string): void {
    this.statusBox.setContent(message);
    
    if (this.container.screen) {
      this.container.screen.render();
    }
    
    // Clear status after 3 seconds
    setTimeout(() => {
      this.statusBox.setContent('Options: (y)es, (n)o, (a)lways, (e)xplain, (q)uit | Press Enter to confirm');
      if (this.container.screen) {
        this.container.screen.render();
      }
    }, 3000);
  }

  /**
   * Show the review dialog
   */
  show(): void {
    this.container.show();
    this.inputBox.focus();
    
    if (this.container.screen) {
      this.container.screen.render();
    }
  }

  /**
   * Hide the review dialog
   */
  hide(): void {
    this.container.hide();
    
    if (this.container.screen) {
      this.container.screen.render();
    }
  }

  /**
   * Get the container element
   */
  getElement(): blessed.Widgets.BoxElement {
    return this.container;
  }

  /**
   * Destroy the component
   */
  destroy(): void {
    this.container.destroy();
  }
}

/**
 * Create a command review dialog
 */
export function createCommandReview(
  parent: blessed.Widgets.Node,
  request: ApprovalRequest,
  options: Partial<CommandReviewOptions> = {},
): TerminalChatCommandReview {
  return new TerminalChatCommandReview({
    parent,
    request,
    ...options,
  });
}

/**
 * Show command review dialog and return promise
 */
export function showCommandReview(
  parent: blessed.Widgets.Node,
  request: ApprovalRequest,
): Promise<{ decision: ReviewDecision; alwaysApprove?: boolean }> {
  return new Promise((resolve, reject) => {
    const review = createCommandReview(parent, request, {
      onDecision: (decision, alwaysApprove) => {
        resolve({ decision, alwaysApprove });
      },
      onExplain: () => {
        // Could show additional explanation dialog
        console.log('Explanation requested for command:', formatCommandForDisplay(request.command));
      },
    });
    
    review.show();
    
    // Auto-reject after 5 minutes
    setTimeout(() => {
      review.destroy();
      reject(new Error('Command review timed out'));
    }, 300000);
  });
}

export default TerminalChatCommandReview;
