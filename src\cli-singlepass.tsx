/**
 * Single-Pass Mode Implementation
 * 
 * Experimental full-context mode that loads entire codebase
 * and applies multiple changes in one pass.
 */

import { readdirSync, statSync, readFileSync } from 'fs';
import { join, relative, extname } from 'path';
import chalk from 'chalk';
import { createOpenAIClient } from './utils/openai-client.js';
import { applyPatch } from './utils/agent/apply-patch.js';
import { logInfo, logError, createTimer } from './utils/logger/log.js';
import { computeSizeMap } from './utils/singlepass/context-limit.js';
import { processFileOperations } from './utils/singlepass/file-ops.js';
import type { AppConfig, FileContent } from './types/index.js';

export interface SinglePassConfig {
  originalPrompt: string;
  config: AppConfig;
  rootPath: string;
  maxFiles?: number;
  maxTotalSize?: number;
  excludePatterns?: string[];
}

/**
 * Run single-pass mode with full context
 */
export async function runSinglePass(options: SinglePassConfig): Promise<void> {
  const timer = createTimer('single-pass-execution');
  
  try {
    console.log(chalk.blue('🚀 Starting Single-Pass Mode'));
    console.log(chalk.gray(`Root: ${options.rootPath}`));
    console.log(chalk.gray(`Prompt: ${options.originalPrompt.substring(0, 100)}...`));
    console.log('');

    // Load codebase files
    console.log(chalk.yellow('📁 Loading codebase...'));
    const files = await loadCodebaseFiles(options);
    
    console.log(chalk.green(`✓ Loaded ${files.length} files`));
    console.log('');

    // Build context
    console.log(chalk.yellow('🧠 Building context...'));
    const context = buildFullContext(options.originalPrompt, files);
    
    console.log(chalk.green(`✓ Context built (${context.length} characters)`));
    console.log('');

    // Process with AI
    console.log(chalk.yellow('🤖 Processing with AI...'));
    const response = await processWithAI(context, options.config);
    
    console.log(chalk.green('✓ AI processing complete'));
    console.log('');

    // Apply changes
    console.log(chalk.yellow('📝 Applying changes...'));
    const result = await applyChanges(response, options.rootPath);
    
    if (result.success) {
      console.log(chalk.green('✅ Single-pass mode completed successfully'));
      console.log(chalk.gray(`Files modified: ${result.filesModified.length}`));
      console.log(chalk.gray(`Files created: ${result.filesCreated.length}`));
      console.log(chalk.gray(`Files deleted: ${result.filesDeleted.length}`));
    } else {
      console.log(chalk.red('❌ Single-pass mode failed'));
      result.errors.forEach(error => console.log(chalk.red(`  - ${error}`)));
    }

    timer.end();

  } catch (error) {
    timer.end();
    const err = error as Error;
    logError('Single-pass mode failed', err);
    console.error(chalk.red('❌ Single-pass mode failed:'), err.message);
    process.exit(1);
  }
}

/**
 * Load codebase files with filtering and size limits
 */
async function loadCodebaseFiles(options: SinglePassConfig): Promise<FileContent[]> {
  const files: FileContent[] = [];
  const maxFiles = options.maxFiles || 100;
  const maxTotalSize = options.maxTotalSize || 1024 * 1024; // 1MB
  const excludePatterns = options.excludePatterns || [
    'node_modules',
    '.git',
    'dist',
    'build',
    '.next',
    'coverage',
    '.nyc_output',
    'logs',
    '*.log',
    '*.lock',
    '*.tmp',
    '*.cache',
  ];

  let totalSize = 0;

  function shouldExclude(filePath: string): boolean {
    const relativePath = relative(options.rootPath, filePath);
    
    return excludePatterns.some(pattern => {
      if (pattern.includes('*')) {
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        return regex.test(relativePath);
      }
      return relativePath.includes(pattern);
    });
  }

  function isTextFile(filePath: string): boolean {
    const textExtensions = [
      '.js', '.ts', '.tsx', '.jsx', '.py', '.java', '.c', '.cpp', '.h', '.hpp',
      '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala', '.html',
      '.htm', '.css', '.scss', '.sass', '.less', '.xml', '.json', '.yaml',
      '.yml', '.toml', '.ini', '.cfg', '.conf', '.md', '.txt', '.sh', '.bash',
      '.zsh', '.fish', '.ps1', '.bat', '.cmd', '.sql', '.r', '.m', '.pl',
      '.lua', '.vim', '.dockerfile', '.gitignore', '.gitattributes',
    ];

    const ext = extname(filePath).toLowerCase();
    return textExtensions.includes(ext) || !ext; // Include files without extensions
  }

  function walkDirectory(dirPath: string): void {
    if (files.length >= maxFiles || totalSize >= maxTotalSize) {
      return;
    }

    try {
      const entries = readdirSync(dirPath);

      for (const entry of entries) {
        const fullPath = join(dirPath, entry);
        
        if (shouldExclude(fullPath)) {
          continue;
        }

        const stats = statSync(fullPath);

        if (stats.isDirectory()) {
          walkDirectory(fullPath);
        } else if (stats.isFile() && isTextFile(fullPath)) {
          if (stats.size > 100 * 1024) { // Skip files larger than 100KB
            continue;
          }

          if (totalSize + stats.size > maxTotalSize) {
            continue;
          }

          try {
            const content = readFileSync(fullPath, 'utf8');
            const relativePath = relative(options.rootPath, fullPath);

            files.push({
              path: relativePath,
              content,
              size: stats.size,
              mimeType: 'text/plain',
            });

            totalSize += stats.size;
          } catch (error) {
            // Skip files that can't be read
            logError(`Failed to read file: ${fullPath}`, error as Error);
          }
        }

        if (files.length >= maxFiles) {
          break;
        }
      }
    } catch (error) {
      logError(`Failed to read directory: ${dirPath}`, error as Error);
    }
  }

  walkDirectory(options.rootPath);

  // Sort files by importance (prioritize certain file types)
  files.sort((a, b) => {
    const getImportance = (file: FileContent): number => {
      const ext = extname(file.path).toLowerCase();
      const name = file.path.toLowerCase();

      if (name.includes('readme') || name.includes('package.json')) {return 10;}
      if (['.ts', '.tsx', '.js', '.jsx'].includes(ext)) {return 8;}
      if (['.py', '.java', '.go', '.rs'].includes(ext)) {return 7;}
      if (['.md', '.txt'].includes(ext)) {return 6;}
      if (['.json', '.yaml', '.yml'].includes(ext)) {return 5;}
      return 3;
    };

    return getImportance(b) - getImportance(a);
  });

  return files;
}

/**
 * Build full context for AI processing
 */
function buildFullContext(prompt: string, files: FileContent[]): string {
  const contextParts: string[] = [];

  // Add system prompt
  contextParts.push('You are an expert software engineer. Analyze the provided codebase and implement the requested changes.');
  contextParts.push('');

  // Add user prompt
  contextParts.push('## User Request');
  contextParts.push(prompt);
  contextParts.push('');

  // Add codebase structure
  contextParts.push('## Codebase Structure');
  files.forEach(file => {
    contextParts.push(`- ${file.path} (${file.size} bytes)`);
  });
  contextParts.push('');

  // Add file contents
  contextParts.push('## File Contents');
  files.forEach(file => {
    contextParts.push(`### ${file.path}`);
    contextParts.push('```');
    contextParts.push(file.content);
    contextParts.push('```');
    contextParts.push('');
  });

  // Add instructions
  contextParts.push('## Instructions');
  contextParts.push('Please analyze the codebase and implement the requested changes.');
  contextParts.push('Provide your response as a unified diff or patch format that can be applied to the files.');
  contextParts.push('Include all necessary file operations (create, update, delete, move).');

  return contextParts.join('\n');
}

/**
 * Process context with AI
 */
async function processWithAI(context: string, config: AppConfig): Promise<string> {
  const client = createOpenAIClient({
    provider: config.provider,
    timeout: 120000, // 2 minutes for complex processing
  });

  try {
    const response = await client.chat.completions.create({
      model: config.model,
      messages: [
        {
          role: 'user',
          content: context,
        },
      ],
      max_tokens: 8192,
      temperature: 0.1, // Low temperature for consistent code generation
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No response content received from AI');
    }

    return content;
  } catch (error) {
    logError('AI processing failed', error as Error);
    throw error;
  }
}

/**
 * Apply changes from AI response
 */
async function applyChanges(response: string, rootPath: string): Promise<any> {
  try {
    // Try to apply as patch first
    const patchResult = await applyPatch(response, rootPath, true);
    
    if (patchResult.success) {
      return patchResult;
    }

    // Try to process as file operations
    const fileOpsResult = await processFileOperations(response, rootPath);
    
    return fileOpsResult;

  } catch (error) {
    const err = error as Error;
    logError('Failed to apply changes', err);
    return {
      success: false,
      errors: [err.message],
      filesModified: [],
      filesCreated: [],
      filesDeleted: [],
    };
  }
}

/**
 * Validate single-pass configuration
 */
export function validateSinglePassConfig(config: SinglePassConfig): string[] {
  const errors: string[] = [];

  if (!config.originalPrompt || config.originalPrompt.trim().length === 0) {
    errors.push('Original prompt is required');
  }

  if (!config.rootPath) {
    errors.push('Root path is required');
  }

  if (config.maxFiles && config.maxFiles < 1) {
    errors.push('Max files must be positive');
  }

  if (config.maxTotalSize && config.maxTotalSize < 1024) {
    errors.push('Max total size must be at least 1KB');
  }

  return errors;
}
